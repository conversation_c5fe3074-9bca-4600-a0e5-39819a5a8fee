# The Predator's Ascent

A dark, interactive text-based story game with a sophisticated system-based progression mechanic.

### Option 2: Node.js Server
1. Make sure you have Node.js installed
2. Run the following commands:
   ```bash
   npm install
   npm start
   ```
3. The game will open in your browser at `http://localhost:8000`

### Option 3: Manual Server Setup
If you have another HTTP server installed, you can use any of these:

```bash
# Using Python 3
python -m http.server 8000

# Using Python 2
python -m SimpleHTTPServer 8000

# Using Node.js http-server
npx http-server -p 8000 -o

# Using PHP
php -S localhost:8000
```

## ⚠️ Important Notes

- **DO NOT** open `index.html` directly in your browser (file:// protocol)
- This will cause CORS errors due to ES6 module restrictions
- Always use an HTTP server to run the game properly

## 🎮 How to Play

1. Read the story text carefully
2. Click on character names (highlighted in gold) to view their profiles
3. Make choices by clicking the golden choice buttons
4. Monitor your stats in the HUD at the bottom:
   - **CP**: Corruption Points
   - **SP**: System Points  
   - **DXP**: Dominance Experience Points
5. Use the System Shop to purchase items and abilities

## 🎨 Features

- **Immersive UI**: Modern dark theme with smooth animations
- **Character Profiles**: Interactive modal system for character information
- **Progressive Story**: Multi-chapter narrative with branching choices
- **Stats System**: Track your character's progression
- **Responsive Design**: Works on desktop and mobile devices
- **Smooth Animations**: Polished transitions and effects

## 🛠️ Technical Details

- Built with vanilla JavaScript ES6 modules
- CSS3 animations and modern styling
- Modular chapter system for easy content expansion
- No external dependencies required

## 📁 Project Structure

```
├── index.html          # Main game page
├── style.css          # All styling and animations
├── server.py          # Python HTTP server
├── start-server.bat   # Windows batch file to start server
├── package.json       # Node.js configuration
├── js/
│   ├── main.js        # Game engine and chapter loading
│   ├── ui.js          # User interface and rendering
│   └── player.js      # Player stats and data management
└── story/
    ├── chapter1.js    # Story content for chapter 1
    ├── chapter2.js    # Story content for chapter 2
    └── ...            # Additional chapters
```

## 🔧 Troubleshooting

### CORS Errors
- Make sure you're using an HTTP server, not opening the file directly
- Check that the server is running on the correct port
- Try a different port if 8000 is already in use

### JavaScript Errors
- Check the browser console (F12) for detailed error messages
- Ensure all files are in the correct locations
- Verify that the server is serving files with correct MIME types

### Performance Issues
- Close other browser tabs to free up memory
- Try a different browser if animations are choppy
- Disable browser extensions that might interfere

## 📝 License

This project is for educational and entertainment purposes.

---

**Enjoy the game! 🎮**
