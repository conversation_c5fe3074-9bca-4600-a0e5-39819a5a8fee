# .gitignore

```
node_modules/
```

# index.html

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>The Predator's Ascent</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>

    <div class="container" id="story-container">
        <!-- The game engine will populate this area -->
    </div>

    <div class="hud" id="hud">
        <div class="hud-item"><strong>Location:</strong> <span id="hud-location"></span></div>
        <div class="hud-item"><strong>Time:</strong> <span id="hud-time"></span></div>
        <div class="hud-item"><strong>CP:</strong> <span id="hud-cp"></span></div>
        <div class="hud-item"><strong>SP:</strong> <span id="hud-sp"></span></div>
        <div class="hud-item"><strong>DXP:</strong> <span id="hud-dxp"></span></div>
    </div>

    <div id="modal-container">
        <!-- Modals will be generated here -->
    </div>

    <script type="module" src="js/main.js"></script>
</body>
</html>
```

# js\main.js

```js
import { player } from './player.js';
import { renderScene, showModal, closeModal, setCurrentModals } from './ui.js';

let currentChapterData;
let currentChapterNumber = 1;

// Make UI functions globally accessible for inline HTML onclicks
window.ui = { showModal, closeModal };

export async function loadChapter(chapterNumber) {
    try {
        const chapterModule = await import(`../story/chapter${chapterNumber}.js`);
        currentChapterData = chapterModule.chapterData;
        setCurrentModals(chapterModule.modals || {});
        
        console.log(`Chapter ${chapterNumber} loaded successfully.`);
        renderScene('start', currentChapterData['start']);
    } catch (error) {
        console.error("Failed to load chapter:", error);
        storyContainer.innerHTML = `<p>Error: Could not load Chapter ${chapterNumber}. File may be missing or contain an error.</p>`;
    }
}

export function makeChoice(choice) {
    if (choice.leadsTo === "LOAD_NEXT_CHAPTER") {
        currentChapterNumber = choice.chapter;
        loadChapter(currentChapterNumber);
    } else {
        renderScene(choice.leadsTo, currentChapterData[choice.leadsTo]);
    }
}

// Initial game start
loadChapter(1);
```

# js\player.js

```js
export const player = {
    stats: {
        cp: 0,
        sp: 0,
        dxp: 0,
    },
    inventory: [],
    skills: [],
    quests: [],
    location: "2BHK Flat, Malviya Nagar",
    time: "09:13 PM",
};
```

# js\ui.js

```js
import { player } from './player.js';
import { makeChoice } from './main.js';

const storyContainer = document.getElementById('story-container');
const hudElements = {
    location: document.getElementById('hud-location'),
    time: document.getElementById('hud-time'),
    cp: document.getElementById('hud-cp'),
    sp: document.getElementById('hud-sp'),
    dxp: document.getElementById('hud-dxp'),
};
const modalContainer = document.getElementById('modal-container');

let currentModals = {};

export function updateHUD() {
    hudElements.location.textContent = player.location;
    hudElements.time.textContent = player.time;
    hudElements.cp.textContent = player.stats.cp;
    hudElements.sp.textContent = player.stats.sp;
    hudElements.dxp.textContent = player.stats.dxp;
}

export function renderScene(sceneId, sceneData) {
    if (!sceneData) {
        console.error(`Scene data not found for sceneId: ${sceneId}`);
        return;
    }

    // Add loading animation
    storyContainer.style.opacity = '0';
    storyContainer.style.transform = 'translateY(20px)';

    setTimeout(() => {
        if (sceneData.onLoad) {
            sceneData.onLoad();
        }

        let html = `<div id="${sceneId}" class="scene-content">${sceneData.text}</div>`;

        if (sceneData.choices && sceneData.choices.length > 0) {
            html += `<div class="choice-section">`;
            sceneData.choices.forEach((choice, index) => {
                // We pass the entire choice object to makeChoice now
                html += `<a href="#" class="choice-button" data-choice-index="${index}">${choice.label}</a>`;
            });
            html += `</div>`;
        }

        storyContainer.innerHTML = html;
        addChoiceListeners(sceneData.choices);
        updateHUD();

        // Animate in the new content
        storyContainer.style.transition = 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)';
        storyContainer.style.opacity = '1';
        storyContainer.style.transform = 'translateY(0)';

        window.scrollTo({ top: 0, behavior: 'smooth' });
    }, 150);
}

function addChoiceListeners(choices) {
    if (!choices) return;
    const buttons = document.querySelectorAll('.choice-button');
    buttons.forEach((button, index) => {
        // Add staggered animation for choice buttons
        button.style.opacity = '0';
        button.style.transform = 'translateY(20px)';

        setTimeout(() => {
            button.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
            button.style.opacity = '1';
            button.style.transform = 'translateY(0)';
        }, 300 + (index * 100));

        button.addEventListener('click', (event) => {
            event.preventDefault();

            // Add click feedback
            button.style.transform = 'scale(0.95)';
            setTimeout(() => {
                button.style.transform = 'scale(1)';
            }, 150);

            const choiceIndex = event.currentTarget.getAttribute('data-choice-index');

            // Disable all buttons to prevent double-clicking
            buttons.forEach(btn => {
                btn.style.pointerEvents = 'none';
                btn.style.opacity = '0.6';
            });

            setTimeout(() => {
                makeChoice(choices[choiceIndex]);
            }, 200);
        });
    });
}

export function showModal(modalId) {
    const modalData = currentModals[modalId];
    if (!modalData) return;

    let modalHTML = `
        <div id="${modalId}Modal" class="modal" style="display:block;">
            <div class="modal-content">
                <span class="close-button" onclick="window.ui.closeModal('${modalId}')">&times;</span>
                <div class="modal-header">${modalData.title}</div>
                <div class="modal-body">${modalData.content}</div>
            </div>
        </div>
    `;
    modalContainer.innerHTML = modalHTML;

    // Add click outside to close functionality
    const modal = document.getElementById(modalId + 'Modal');
    modal.addEventListener('click', (event) => {
        if (event.target === modal) {
            closeModal(modalId);
        }
    });

    // Add escape key to close functionality
    const escapeHandler = (event) => {
        if (event.key === 'Escape') {
            closeModal(modalId);
            document.removeEventListener('keydown', escapeHandler);
        }
    };
    document.addEventListener('keydown', escapeHandler);
}

export function closeModal(modalId) {
    const modal = document.getElementById(modalId + 'Modal');
    if (modal) {
        modal.style.animation = 'fadeOut 0.3s ease-out';
        setTimeout(() => {
            modal.style.display = 'none';
            modalContainer.innerHTML = '';
        }, 300);
    } else {
        modalContainer.innerHTML = '';
    }
}

export function setCurrentModals(modals) {
    currentModals = modals;
}
```

# package.json

```json
{
  "name": "the-predators-ascent",
  "version": "1.0.0",
  "description": "A text-based interactive story game",
  "main": "index.html",
  "scripts": {
    "start": "npx http-server -p 8000 -o",
    "dev": "npx http-server -p 8000 -o -c-1"
  },
  "keywords": ["game", "interactive", "story"],
  "author": "",
  "license": "MIT",
  "devDependencies": {
    "http-server": "^14.1.1"
  }
}

```

# README.md

```md
# The Predator's Ascent

A dark, interactive text-based story game with a sophisticated system-based progression mechanic.

### Option 2: Node.js Server
1. Make sure you have Node.js installed
2. Run the following commands:
   \`\`\`bash
   npm install
   npm start
   \`\`\`
3. The game will open in your browser at `http://localhost:8000`

### Option 3: Manual Server Setup
If you have another HTTP server installed, you can use any of these:

\`\`\`bash
# Using Python 3
python -m http.server 8000

# Using Python 2
python -m SimpleHTTPServer 8000

# Using Node.js http-server
npx http-server -p 8000 -o

# Using PHP
php -S localhost:8000
\`\`\`

## ⚠️ Important Notes

- **DO NOT** open `index.html` directly in your browser (file:// protocol)
- This will cause CORS errors due to ES6 module restrictions
- Always use an HTTP server to run the game properly

## 🎮 How to Play

1. Read the story text carefully
2. Click on character names (highlighted in gold) to view their profiles
3. Make choices by clicking the golden choice buttons
4. Monitor your stats in the HUD at the bottom:
   - **CP**: Corruption Points
   - **SP**: System Points  
   - **DXP**: Dominance Experience Points
5. Use the System Shop to purchase items and abilities

## 🎨 Features

- **Immersive UI**: Modern dark theme with smooth animations
- **Character Profiles**: Interactive modal system for character information
- **Progressive Story**: Multi-chapter narrative with branching choices
- **Stats System**: Track your character's progression
- **Responsive Design**: Works on desktop and mobile devices
- **Smooth Animations**: Polished transitions and effects

## 🛠️ Technical Details

- Built with vanilla JavaScript ES6 modules
- CSS3 animations and modern styling
- Modular chapter system for easy content expansion
- No external dependencies required

## 📁 Project Structure

\`\`\`
├── index.html          # Main game page
├── style.css          # All styling and animations
├── server.py          # Python HTTP server
├── start-server.bat   # Windows batch file to start server
├── package.json       # Node.js configuration
├── js/
│   ├── main.js        # Game engine and chapter loading
│   ├── ui.js          # User interface and rendering
│   └── player.js      # Player stats and data management
└── story/
    ├── chapter1.js    # Story content for chapter 1
    ├── chapter2.js    # Story content for chapter 2
    └── ...            # Additional chapters
\`\`\`

## 🔧 Troubleshooting

### CORS Errors
- Make sure you're using an HTTP server, not opening the file directly
- Check that the server is running on the correct port
- Try a different port if 8000 is already in use

### JavaScript Errors
- Check the browser console (F12) for detailed error messages
- Ensure all files are in the correct locations
- Verify that the server is serving files with correct MIME types

### Performance Issues
- Close other browser tabs to free up memory
- Try a different browser if animations are choppy
- Disable browser extensions that might interfere

## 📝 License

This project is for educational and entertainment purposes.

---

**Enjoy the game! 🎮**

```

# story\chapter1.js

```js
export const chapterData = {
    "start": {
        "text": `
            <p>The pain in your leg is a dull, constant throb, a phantom echo of the crash. It’s been ten days since the funeral. Ten days of living in this small, suffocating flat that smells of Dettol, incense sticks, and your mother’s quiet, unending grief.</p>
            <p>You lie on your bed, staring at the peeling paint on the ceiling. The whirring of the old Usha fan does little to cut through the oppressive late-August humidity. <em>Normal sounds. A normal, shitty life.</em></p>
            <p>Except for the thing that lives in your head now.</p>
            <div class="system-prompt">
                <div class="prompt-title">[Daily Login Bonus Available]</div>
                You've ignored these for days, writing them off as trauma-induced hallucinations. But the threat of another splitting headache forces your hand.
            </div>
        `,
        "choices": [
            { "label": "Claim the Bonus.", "leadsTo": "choice1_result" }
        ]
    },
    "choice1_result": {
        "onLoad": function() {
            window.player.stats.cp += 10;
            window.player.stats.sp += 1;
        },
        "text": `
            <p>With a sense of weary resignation, you focus your intent. <em>Fine. Yes. Claim.</em></p>
            <div class="system-prompt">
                <div class="prompt-title">[Daily Login Bonus Claimed!]</div>
                <span class="reward">[+10 Corruption Points (CP) added.]</span><br>
                <span class="reward">[+1 System Point (SP) added.]</span>
            </div>
            <p>A strange, faint warmth spreads through your chest. It's immediately followed by a new, more insistent prompt.</p>
            <div class="system-prompt">
                <div class="prompt-title">[Tutorial Quest Chain Initiated: The World Through New Eyes]</div>
                <strong>Quest 1: Know Your Assets.</strong><br>
                <strong>Objective:</strong> View the profiles of the three female occupants of this dwelling: <span class="character-name" onclick="window.ui.showModal('sonia')">Sonia</span>, <span class="character-name" onclick="window.ui.showModal('natasha')">Natasha</span>, and <span class="character-name" onclick="window.ui.showModal('tanya')">Tanya</span>.<br>
                <span class="reward"><strong>Reward:</strong> +50 CP, +2 SP, Skill Unlocked: [Mental Resonance Scan (Lvl 1)].</span><br>
                <span class="penalty"><strong>Failure Penalty:</strong> Persistent Migraine for 24 hours.</span>
            </div>
        `,
        "choices": [
            { "label": "Accept the Quest. View the Profiles.", "leadsTo": "choice2_result" }
        ]
    },
    "choice2_result": {
        "onLoad": function() {
            window.player.stats.cp += 50;
            window.player.stats.sp += 2;
            window.player.skills.push("Mental Resonance Scan (Lvl 1)");
        },
        "text": `
            <p>You accept. The system complies instantly. You click on their names, and the truth is laid bare.</p>
            <div class="system-prompt">
                <div class="prompt-title">[QUEST COMPLETE: Know Your Assets]</div>
                <span class="reward">[+50 CP, +2 SP received.]</span><br>
                <span class="reward">[Skill Unlocked: [Mental Resonance Scan (Lvl 1)]]</span>
            </div>
            <p>You now see them not as family, but as a collection of vulnerabilities. The knowledge is disgusting, and it gives you a terrifying sense of <strong>power</strong>.</p>
            <div class="system-prompt">
                <div class="prompt-title">[Tutorial Quest Chain Updated]</div>
                <strong>Quest 2: The First Tool.</strong><br>
                <strong>Objective:</strong> Open the [System Shop] and purchase your first item.
            </div>
        `,
        "choices": [
            { "label": "Open the System Shop.", "leadsTo": "choice3_result" }
        ]
    },
    "choice3_result": {
        "text": `
             <p>The Shop menu opens in your mind's eye. You have 60 CP to spend.</p>
             <p>Under [Consumables], you see a list:</p>
             <ul><li>[Sleeping Pills (Mild)] - 15 CP</li><li>[Laxative Powder (Odorless)] - 20 CP</li><li>[Aphrodisiac (Low Grade)] - 50 CP</li></ul>
             <p>Control is more important than immediate gratification.</p>
        `,
        "choices": [
            { "label": "Purchase [Sleeping Pills (Mild)] x2 for 30 CP.", "leadsTo": "final" }
        ]
    },
    "final": {
        "onLoad": function() {
            window.player.stats.cp -= 30;
            window.player.inventory.push({ item: "Sleeping Pills (Mild)", quantity: 2 });
        },
        "text": `
            <div class="system-prompt">
                <div class="prompt-title">[Purchase Confirmed!]</div>
                [-30 CP. Remaining Balance: 30 CP]<br>
                [Item has been materialized in your bedside drawer.]<br>
                [QUEST COMPLETE: The First Tool]
            </div>
            <p>You open your bedside drawer. There they are. Two small, unmarked pills in a tiny ziplock bag. They are real. The system is real. The power is real.</p>
            <hr>
        `,
        "choices": [
            { "label": "Proceed to Chapter 2", "leadsTo": "LOAD_NEXT_CHAPTER", "chapter": 2 }
        ]
    }
};

export const modals = {
    "sonia": {"title": "[Character Profile: Sonia Singh]","content": `<p><strong>Designation:</strong> Mother. Primary Asset (Potential).</p><p><strong>Age:</strong> 42</p><p><strong>Status:</strong> Widowed. Financially Stressed (Perceived). <strong>Sexually Starved (Critical).</strong></p><p><strong>Asset Evaluation:</strong> Prime MILF Physique. Heavy, sagging breasts (38D), wide birthing hips (40), soft belly. Years of sexual neglect have created a deep-seated vulnerability. Prime for corruption.</p>`},
    "natasha": {"title": "[Character Profile: Natasha Singh]","content": `<p><strong>Designation:</strong> Sister. Secondary Asset.</p><p><strong>Age:</strong> 24</p><p><strong>Status:</strong> Underpaid HR Executive. Frustrated with life. Desperate for male validation and a wealthier lifestyle.</p><p><strong>Asset Evaluation:</strong> Voluptuous, top-heavy build (36D). Projects a 'sanskari' image but possesses a hidden slutty streak.</p>`},
    "tanya": {"title": "[Character Profile: Tanya Singh]","content": `<p><strong>Designation:</strong> Sister. Tertiary Asset.</p><p><strong>Age:</strong> 22</p><p><strong>Status:</strong> Underpaid HR Assistant. Fiery temper. Deep-seated inferiority complex masked by aggression.</p><p><strong>Asset Evaluation:</strong> Petite, athletic frame (32B). A 'chota packet, bada dhamaka'. Responds to dominance.</p>`}
};
```

# story\chapter2.js

```js
export const chapterData = {
    "start": {
        "onLoad": function() {
            window.player.location = "Home";
            window.player.time = "Afternoon";
        },
        "text": `
            <p>Two weeks pass. The System is quiet, but you are not. Using your [Mental Resonance Scan], you listen to your family's anxieties, their petty jealousies, their desperation. You see only weakness.</p>
            <p>The day of your final hospital check-up arrives, and you are officially discharged. That afternoon, a man in an impeccably tailored suit arrives. Advocate V.K. Mehra, your father's personal lawyer.</p>
            <p>"Mrs. Singh, ladies, if you would excuse us," he says politely but firmly. "My client's instructions were explicit. This conversation is for Vikram's ears alone."</p>
            <p>Alone in the living room, he opens his briefcase.</p>
        `,
        "choices": [
            { "label": "Listen to the lawyer.", "leadsTo": "revelation" }
        ]
    },
    "revelation": {
        "text": `
            <p>"Vikram," the lawyer begins, "Your father was not who he seemed. He was a discreet and exceptionally successful investor. His will is simple. You, his only son, are the sole beneficiary of his entire private estate."</p>
            <p>He slides a thick file across the table. You see numbers that make your head spin. Offshore accounts. Property deeds. A stock portfolio.</p>
            <p>"What... what is the total value?" you ask, your voice a whisper.</p>
            <p>Advocate Mehra looks you straight in the eye. "As of this morning's valuation, just over <strong>two hundred and twelve crore rupees.</strong>"</p>
            <div class="system-prompt">
                <div class="prompt-title">[World Event Triggered: The Inheritance]</div>
                <span class="reward">[Massive Shift in Personal Status Detected: Pauper -> Crorepati]</span><br>
                <span class="reward">[New Feature Unlocked: [Wealth Conversion]]</span><br>
                <span class="reward">[Title Unlocked: The Hidden King]</span>
            </div>
            <p>The grief for your father is replaced by a chilling clarity. You see the path forward.</p>
        `,
        "choices": [
            { "label": "Make your decision.", "leadsTo": "decision" }
        ]
    },
    "decision": {
        "onLoad": function() {
            window.player.skills.push("Deception (Lvl 2)");
        },
        "text": `
            <p>"Mr. Mehra," you say, your voice now firm, devoid of any youthful tremor. "No one is to know about this. Not my mother, not my sisters. My father's 'official' life is the only one they will ever know. Is that understood?"</p>
            <p>The lawyer smiles faintly. "Your secret is safe, Mr. Singh."</p>
            <p>After he leaves, you lie to your family, telling them it was just about pension formalities. The lie comes as easily as breathing. You are the Hidden King. You will let them suffer, let them squirm, and make them dance for scraps from a fortune they can't imagine.</p>
            <p>That night, a quest you were too scared to accept before now seems like the only logical step.</p>
            <div class="system-prompt">
                <div class="prompt-title">[Chain Quest Initiated: The Queen's Conquest (Stage 1)]</div>
                <strong>Description:</strong> A king's first conquest is his own domain. To control the kingdom, you must first own its heart, body, and soul.<br>
                <strong>Objective:</strong> Breach the first wall of her sanctity. Administer one dose of [Sleeping Pills (Mild)] into her nightly glass of milk.<br>
                <span class="reward"><strong>Reward:</strong> +200 CP, +200 DXP, Unlocks Quest Stage 2.</span>
            </div>
        `,
        "choices": [
            { "label": "Accept the Quest. Her fall begins tonight.", "leadsTo": "final" }
        ]
    },
    "final": {
        "onLoad": function() {
            window.player.quests.push("The Queen's Conquest");
        },
        "text": `
            <p>You accept. Your heart hammers against your ribs, a primal drumbeat of fear and <strong>exhilarating, monstrous excitement</strong>. You are not her son anymore. You are a force of nature, and she is in your path.</p>
            <hr>
        `,
        "choices": [
            { "label": "Proceed to Chapter 3", "leadsTo": "LOAD_NEXT_CHAPTER", "chapter": 3 }
        ]
    }
};
export const modals = {};
```

# story\chapter3.js

```js
export const chapterData = {
    "start": {
        "onLoad": function() {
            window.player.time = "10:47 PM";
        },
        "text": `
            <p>The kitchen is quiet. You stand before the gas stove, a saucepan of Amul Gold milk simmering gently. You crush one of the pills from your drawer and scrape the fine white powder into the milk, watching it dissolve instantly.</p>
            <p>But your mind, now wired to the System’s cold logic, sees an opportunity. A way to make this act not just about submission, but about <strong>infection</strong>.</p>
            <div class="system-prompt">
                <div class="prompt-title">[Secret Bonus Objective Detected!]</div>
                <strong>Objective: The Serpent's Seed.</strong><br>
                <strong>Task:</strong> Lace the target's milk not just with the sedative, but with your own genetic essence.<br>
                <span class="reward"><strong>Bonus Reward:</strong> Skill Unlocked: [Essence of Corruption (Lvl 1)].</span>
            </div>
            <p>The System isn't just a tool; it's a kindred spirit, rewarding your most depraved instincts. You are alone. The choice is yours.</p>
        `,
        "choices": [
            { "label": "Just use the pill. Keep it simple.", "leadsTo": "simple_path" },
            { "label": "Defile the milk. Claim the bonus.", "leadsTo": "corrupt_path" }
        ]
    },
    "simple_path": {
        "text": `<p>You decide against it. For now. You pour the drugged milk into a glass and call your mother. She drinks it gratefully, pats your cheek, and goes to her room. You have breached her trust, but not her body. The night awaits.</p>`,
        "choices": [
            { "label": "Wait for the sedative to take effect.", "leadsTo": "wait_for_sleep" }
        ]
    },
    "corrupt_path": {
        "onLoad": function() {
            window.player.skills.push("Essence of Corruption (Lvl 1)");
        },
        "text": `
            <p>A low chuckle escapes your lips. You unzip your trousers, your cock already hard. It's the ultimate act of desecration. You finish quickly, your hot, thick sperm coiling into the warm milk. You stir it once. Pure. Deceptively so.</p>
            <p>You call your mother. <span class="character-name" onclick="window.ui.showModal('sonia')">Sonia</span> enters, a tired smile on her face. "Jeeta reh, beta," she says, drinking the tainted milk without a second thought. She pats your cheek and walks to her room, completely unaware.</p>
            <div class="system-prompt"><div class="prompt-title">[BONUS OBJECTIVE COMPLETE!]</div><span class="reward">[Skill Unlocked: [Essence of Corruption (Lvl 1)] - Your fluids now carry a psychoactive agent that lowers inhibitions and creates addiction.]</span></div>
        `,
        "choices": [
            { "label": "Wait for the sedative to take effect.", "leadsTo": "wait_for_sleep" }
        ]
    },
    "wait_for_sleep": {
        "text": `
            <p>You watch the System's status bar for your mother. [Entering Stage 4 Deep Sleep]. It's time.</p>
            <p>You slip into her room. The air is thick with her scent. Moonlight filters through the window, painting her sleeping form in shades of silver and grey. She is completely vulnerable. Completely yours.</p>
            <p>You kneel by her bed. You don't touch her. Not yet. You unzip your pants again, your cock springing free. You look at her face—the face that had sung you lullabies—and you paint it with your release. A thick, hot load across her cheeks, her lips, her closed eyelids. It is a baptism. A claiming.</p>
        `,
        "choices": [
            { "label": "Leave the room. The deed is done.", "leadsTo": "final" }
        ]
    },
    "final": {
        "onLoad": function() {
            window.player.stats.cp += 200;
            window.player.stats.dxp += 200;
        },
        "text": `
            <div class="system-prompt">
                <div class="prompt-title">[QUEST COMPLETE: The Queen's Conquest (Stage 1)]</div>
                <span class="reward">[+200 CP, +200 DXP received.]</span><br>
                [Unlocks Quest Stage 2: [The First Touch].]
            </div>
            <p>You slip back into your room, your heart pounding. The next morning, you hear your mother in the bathroom, confused. "Pata nahi," she says to Tanya. "Raat ko bada ajeeb sa sapna aaya. Aur chehre pe ajeeb si chip-chip lag rahi hai."</p>
            <p>You smirk behind your newspaper. The seed has been planted.</p>
            <hr>
        `,
        "choices": [
            { "label": "Proceed to Chapter 4", "leadsTo": "LOAD_NEXT_CHAPTER", "chapter": 4 }
        ]
    }
};
export const modals = {
    "sonia": {"title": "[Character Profile: Sonia Singh]","content": `<p><strong>Designation:</strong> Mother. Primary Asset (Potential).</p><p><strong>Age:</strong> 42</p><p><strong>Status:</strong> Widowed. Financially Stressed (Perceived). <strong>Sexually Starved (Critical).</strong></p><p><strong>Asset Evaluation:</strong> Prime MILF Physique. Heavy, sagging breasts (38D), wide birthing hips (40), soft belly. Years of sexual neglect have created a deep-seated vulnerability. Prime for corruption.</p>`}
};
```

# story\chapter4.js

```js
export const chapterData = {
    "start": {
        "onLoad": function() {
            window.player.time = "The Next Night";
        },
        "text": `
            <p>All day, you notice a change in your mother. She seems restless, distracted. Her eyes linger on you a moment too long. The [Essence of Corruption] is working, stirring something deep within her. The foundation is cracked, ready to be shattered.</p>
            <p>That night, you know what you must do. The second pill feels like a key in your hand. The System, ever the enabler, presents the next stage of your conquest. It's no longer a suggestion. It is a command.</p>
            <div class="system-prompt">
                <div class="prompt-title">[Chain Quest Updated: The Queen's Conquest (Stage 2)]</div>
                <strong>Description:</strong> The seal has been broken. Her subconscious is now primed. It is time to claim the flesh.<br>
                <strong>Objective:</strong> Administer the second dose of [Sleeping Pills (Mild)]. While she sleeps, claim her body.<br>
                <strong>Task 1:</strong> Achieve vaginal penetration.<br>
                <strong>Task 2:</strong> Achieve anal penetration.<br>
                <span class="reward"><strong>Reward:</strong> +500 CP, +1000 DXP, Skill Unlocked: [Primal Dominance (Lvl 1)].</span>
            </div>
            <p>As you read the objectives, another, more profound prompt appears.</p>
        `,
        "choices": [
            { "label": "View the Bonus Objective.", "leadsTo": "bonus" }
        ]
    },
    "bonus": {
        "text": `
            <div class="system-prompt">
                <div class="prompt-title">[Secret Bonus Objective Available!]</div>
                <strong>Objective: The Seed of Servitude.</strong><br>
                <strong>Task:</strong> Finish inside her during both vaginal and anal penetration.<br>
                <strong>Reason:</strong> The amplified dose of your [Essence of Corruption] will shatter her core identity and begin the process of rebuilding her as your devoted slave.<br>
                <span class="reward"><strong>Bonus Reward:</strong> Overpowered Trait Unlocked: [Womb of the Alpha] - Any female impregnated by you will give birth to offspring completely loyal to you, possessing enhanced abilities. A true dynasty.</span>
            </div>
            <p>Your breath catches in your throat. This isn't just about sex anymore. This is about legacy. Dynasty. The System is offering you the chance to become a true patriarch, a god breeding his own race of demigods.</p>
            <p>The decision was made before the choice was even offered.</p>
        `,
        "choices": [
            { "label": "Drug her milk. Begin the final conquest.", "leadsTo": "conquest" }
        ]
    },
    "conquest": {
        "text": `
            <p>The second drugging is effortless. She drinks the milk without suspicion. An hour later, she is lost to the world, a canvas for your desires.</p>
            <p>You enter her room and lock the door. You pull the thin nightie over her head, revealing her full, mature body in the pale moonlight. Her heavy 38D breasts, the soft curve of her belly, the dark thatch of hair between her thick thighs. She is yours.</p>
            <p>You part her legs. Your thick lund finds her wet, slick entrance. She is ready for you, her body unconsciously craving what her mind would deny. You push in, a slow, brutal claiming. Her body convulses in her sleep, but she doesn't wake. You fuck your own mother, each thrust a nail in the coffin of her old life. You fill her chut with your first load, the hot seed of corruption and servitude.</p>
            <p>But you are not done. The quest demands total submission.</p>
        `,
        "choices": [
            { "label": "Turn her over. Claim her completely.", "leadsTo": "final_conquest" }
        ]
    },
    "final_conquest": {
        "onLoad": function() {
            window.player.stats.cp += 500;
            window.player.stats.dxp += 1000;
            window.player.skills.push("Primal Dominance (Lvl 1)");
            window.player.skills.push("Womb of the Alpha");
        },
        "text": `
            <p>You flip her onto her stomach, her large ass raised in the air. You spit on your cock and align it with her tight, puckered asshole. This is the final taboo, the ultimate degradation. You force your way in, tearing her, marking her as your property in the most primal way possible. Her muffled groans are music to your ears. You fuck her gaand without mercy, a king taking his throne. You empty your second load deep inside her, completing the bonus objective.</p>
            <div class="system-prompt">
                <div class="prompt-title">[QUEST COMPLETE: The Queen's Conquest (Stage 2)]</div>
                <span class="reward">[+500 CP, +1000 DXP received.]</span><br>
                <span class="reward">[Skill Unlocked: [Primal Dominance (Lvl 1)]]</span><br>
                <div class="prompt-title">[BONUS OBJECTIVE COMPLETE!]</div>
                <span class="reward">[Overpowered Trait Unlocked: [Womb of the Alpha]]</span>
            </div>
            <p>You pull out, leaving her used and broken in her bed. She will wake up sore, confused, and with a deep, gnawing hunger that only you can satisfy. Her mind may not know it yet, but her body now belongs to you completely.</p>
            <hr>
            <p><strong>END OF ARC 1.</strong></p>
        `
    }
};
export const modals = {};
```

# style.css

```css
/* Import Google Fonts for better typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

* {
    box-sizing: border-box;
}

body {
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #0f0f0f 100%);
    background-attachment: fixed;
    color: #e8e8e8;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 1.1em;
    font-weight: 400;
    line-height: 1.8;
    margin: 0;
    padding: 40px 20px 140px 20px;
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    max-width: 900px;
    margin: 0 auto;
    background: rgba(25, 25, 25, 0.8);
    border-radius: 16px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* --- HUD --- */
.hud {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.95) 0%, rgba(15, 15, 15, 0.98) 100%);
    border-top: 3px solid #d4af37;
    padding: 16px 24px;
    box-sizing: border-box;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-size: 0.95em;
    font-weight: 500;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    z-index: 1000;
    backdrop-filter: blur(20px);
    box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.5);
}

.hud-item {
    margin: 8px 20px;
    color: #f4d03f;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    transition: all 0.3s ease;
}

.hud-item:hover {
    color: #fff;
    transform: translateY(-1px);
}

.hud-item strong {
    color: #b8b8b8;
    font-weight: 600;
    margin-right: 8px;
}

/* --- Story & Prompts --- */
.system-prompt {
    background: linear-gradient(135deg, rgba(20, 40, 80, 0.6) 0%, rgba(30, 50, 90, 0.4) 100%);
    border: 2px solid rgba(102, 170, 255, 0.6);
    border-radius: 12px;
    padding: 24px;
    margin: 32px 0;
    position: relative;
    box-shadow: 0 8px 32px rgba(102, 170, 255, 0.1);
    backdrop-filter: blur(5px);
}

.system-prompt::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(45deg, #66aaff, #4488dd, #66aaff);
    border-radius: 12px;
    z-index: -1;
    opacity: 0.3;
}

.prompt-title {
    color: #88ccff;
    font-weight: 600;
    font-size: 1.1em;
    margin-bottom: 16px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    letter-spacing: 0.5px;
}

.reward {
    color: #4ade80;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.penalty {
    color: #f87171;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* --- Choices --- */
.choice-section {
    margin-top: 40px;
    padding: 32px;
    background: linear-gradient(135deg, rgba(30, 30, 30, 0.8) 0%, rgba(40, 40, 40, 0.6) 100%);
    border-left: 6px solid #d4af37;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.choice-button {
    display: block;
    width: 100%;
    margin: 20px 0;
    padding: 20px 24px;
    background: linear-gradient(135deg, #d4af37 0%, #b38f00 100%);
    color: #000;
    text-align: center;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.05em;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 16px rgba(212, 175, 55, 0.3);
    position: relative;
    overflow: hidden;
}

.choice-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.choice-button:hover {
    background: linear-gradient(135deg, #f4d03f 0%, #d4af37 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(212, 175, 55, 0.4);
}

.choice-button:hover::before {
    left: 100%;
}

.choice-button:active {
    transform: translateY(0);
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

/* --- Special Text --- */
p {
    margin: 1.2em 0;
    text-align: justify;
}

em {
    color: #c0c0c0;
    font-style: italic;
    font-weight: 300;
}

strong {
    color: #ff7b7b;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.character-name {
    color: #d4af37;
    cursor: pointer;
    font-weight: 600;
    text-decoration: none;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    padding: 2px 4px;
    border-radius: 4px;
}

.character-name:hover {
    color: #f4d03f;
    background: rgba(212, 175, 55, 0.1);
    border-bottom-color: #d4af37;
    text-shadow: 0 1px 4px rgba(212, 175, 55, 0.5);
}

/* --- Modals --- */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background: rgba(0, 0, 0, 0.85);
    backdrop-filter: blur(8px);
    animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-content {
    background: linear-gradient(135deg, #1f1f1f 0%, #2a2a2a 100%);
    margin: 8% auto;
    padding: 32px;
    border: 2px solid #d4af37;
    width: 90%;
    max-width: 700px;
    border-radius: 16px;
    font-family: 'Inter', sans-serif;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6);
    position: relative;
    animation: slideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-header {
    color: #d4af37;
    font-size: 1.4em;
    font-weight: 600;
    margin-bottom: 24px;
    border-bottom: 2px solid rgba(212, 175, 55, 0.3);
    padding-bottom: 16px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.close-button {
    color: #888;
    position: absolute;
    top: 20px;
    right: 24px;
    font-size: 28px;
    font-weight: bold;
    line-height: 1;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 4px 8px;
    border-radius: 50%;
}

.close-button:hover,
.close-button:focus {
    color: #fff;
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Scene content animations */
.scene-content {
    animation: slideInContent 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInContent {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* --- Responsive Design --- */
@media (max-width: 768px) {
    body {
        padding: 20px 16px 160px 16px;
        font-size: 1em;
    }

    .container {
        padding: 24px;
        border-radius: 12px;
    }

    .hud {
        padding: 12px 16px;
        font-size: 0.85em;
    }

    .hud-item {
        margin: 6px 12px;
    }

    .choice-button {
        padding: 16px 20px;
        font-size: 1em;
    }

    .modal-content {
        margin: 5% auto;
        padding: 24px;
        width: 95%;
    }

    .system-prompt {
        padding: 20px;
        margin: 24px 0;
    }
}
```

