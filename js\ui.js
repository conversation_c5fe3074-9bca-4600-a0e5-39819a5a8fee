import { player } from './player.js';
import { makeChoice } from './main.js';

const storyContainer = document.getElementById('story-container');
const hudElements = {
    location: document.getElementById('hud-location'),
    time: document.getElementById('hud-time'),
    cp: document.getElementById('hud-cp'),
    sp: document.getElementById('hud-sp'),
    dxp: document.getElementById('hud-dxp'),
};
const modalContainer = document.getElementById('modal-container');

let currentModals = {};

export function updateHUD() {
    hudElements.location.textContent = player.location;
    hudElements.time.textContent = player.time;
    hudElements.cp.textContent = player.stats.cp;
    hudElements.sp.textContent = player.stats.sp;
    hudElements.dxp.textContent = player.stats.dxp;
}

export function renderScene(sceneId, sceneData) {
    if (!sceneData) {
        console.error(`Scene data not found for sceneId: ${sceneId}`);
        return;
    }

    // Add loading animation
    storyContainer.style.opacity = '0';
    storyContainer.style.transform = 'translateY(20px)';

    setTimeout(() => {
        if (sceneData.onLoad) {
            sceneData.onLoad();
        }

        let html = `<div id="${sceneId}" class="scene-content">${sceneData.text}</div>`;

        if (sceneData.choices && sceneData.choices.length > 0) {
            html += `<div class="choice-section">`;
            sceneData.choices.forEach((choice, index) => {
                // We pass the entire choice object to makeChoice now
                html += `<a href="#" class="choice-button" data-choice-index="${index}">${choice.label}</a>`;
            });
            html += `</div>`;
        }

        storyContainer.innerHTML = html;
        addChoiceListeners(sceneData.choices);
        updateHUD();

        // Animate in the new content
        storyContainer.style.transition = 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)';
        storyContainer.style.opacity = '1';
        storyContainer.style.transform = 'translateY(0)';

        window.scrollTo({ top: 0, behavior: 'smooth' });
    }, 150);
}

function addChoiceListeners(choices) {
    if (!choices) return;
    const buttons = document.querySelectorAll('.choice-button');
    buttons.forEach((button, index) => {
        // Add staggered animation for choice buttons
        button.style.opacity = '0';
        button.style.transform = 'translateY(20px)';

        setTimeout(() => {
            button.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
            button.style.opacity = '1';
            button.style.transform = 'translateY(0)';
        }, 300 + (index * 100));

        button.addEventListener('click', (event) => {
            event.preventDefault();

            // Add click feedback
            button.style.transform = 'scale(0.95)';
            setTimeout(() => {
                button.style.transform = 'scale(1)';
            }, 150);

            const choiceIndex = event.currentTarget.getAttribute('data-choice-index');

            // Disable all buttons to prevent double-clicking
            buttons.forEach(btn => {
                btn.style.pointerEvents = 'none';
                btn.style.opacity = '0.6';
            });

            setTimeout(() => {
                makeChoice(choices[choiceIndex]);
            }, 200);
        });
    });
}

export function showModal(modalId) {
    const modalData = currentModals[modalId];
    if (!modalData) return;

    let modalHTML = `
        <div id="${modalId}Modal" class="modal" style="display:block;">
            <div class="modal-content">
                <span class="close-button" onclick="window.ui.closeModal('${modalId}')">&times;</span>
                <div class="modal-header">${modalData.title}</div>
                <div class="modal-body">${modalData.content}</div>
            </div>
        </div>
    `;
    modalContainer.innerHTML = modalHTML;

    // Add click outside to close functionality
    const modal = document.getElementById(modalId + 'Modal');
    modal.addEventListener('click', (event) => {
        if (event.target === modal) {
            closeModal(modalId);
        }
    });

    // Add escape key to close functionality
    const escapeHandler = (event) => {
        if (event.key === 'Escape') {
            closeModal(modalId);
            document.removeEventListener('keydown', escapeHandler);
        }
    };
    document.addEventListener('keydown', escapeHandler);
}

export function closeModal(modalId) {
    const modal = document.getElementById(modalId + 'Modal');
    if (modal) {
        modal.style.animation = 'fadeOut 0.3s ease-out';
        setTimeout(() => {
            modal.style.display = 'none';
            modalContainer.innerHTML = '';
        }, 300);
    } else {
        modalContainer.innerHTML = '';
    }
}

export function setCurrentModals(modals) {
    currentModals = modals;
}