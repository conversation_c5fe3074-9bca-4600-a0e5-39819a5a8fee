import { player } from './player.js';
import { renderScene, showModal, closeModal, setCurrentModals } from './ui.js';

let currentChapterData;
let currentChapterNumber = 1;

// Make UI functions globally accessible for inline HTML onclicks
window.ui = { showModal, closeModal };

export async function loadChapter(chapterNumber) {
    try {
        const chapterModule = await import(`../story/chapter${chapterNumber}.js`);
        currentChapterData = chapterModule.chapterData;
        setCurrentModals(chapterModule.modals || {});
        
        console.log(`Chapter ${chapterNumber} loaded successfully.`);
        renderScene('start', currentChapterData['start']);
    } catch (error) {
        console.error("Failed to load chapter:", error);
        storyContainer.innerHTML = `<p>Error: Could not load Chapter ${chapterNumber}. File may be missing or contain an error.</p>`;
    }
}

export function makeChoice(choice) {
    if (choice.leadsTo === "LOAD_NEXT_CHAPTER") {
        currentChapterNumber = choice.chapter;
        loadChapter(currentChapterNumber);
    } else {
        renderScene(choice.leadsTo, currentChapterData[choice.leadsTo]);
    }
}

// Initial game start
loadChapter(1);